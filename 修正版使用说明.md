# 协变量预测修正版使用说明

## 概述

本脚本 `协变量预测_修正版.py` 是对原始协变量预测代码的修正版本，主要解决了以下三个关键问题：

1. **消除重复标准化问题**
2. **添加预测结果反标准化**
3. **修正评估指标计算方式**

## 主要修正内容

### 1. 重复标准化问题修正

**原始问题：**
- 数据在 `scale_result_dict()` 中被标准化一次
- 在 `standardize_data()` 中又被标准化一次
- 导致数据分布失真，scaler含义混乱

**修正方案：**
```python
# 只在数据预处理阶段进行一次标准化
def load_and_preprocess_data():
    # 对每个国家的数据进行一次标准化
    scaler = MinMaxScaler()
    scaled_features = scaler.fit_transform(features)
    # 保存scaler用于后续反标准化
    scalers_dict[country] = scaler
```

### 2. 预测结果反标准化

**原始问题：**
- 预测结果保持在[0,1]标准化尺度
- 无法与原始数据进行有意义的比较

**修正方案：**
```python
# 在评估前进行反标准化
true_vals_original = scaler.inverse_transform(true_vals_scaled)
pred_vals_original = scaler.inverse_transform(pred_vals_scaled)
```

### 3. 评估指标计算修正

**原始问题：**
- 评估指标在[0,1]标准化尺度上计算
- MSE、RMSE、MAE数值被人为压缩
- 结果缺乏实际意义

**修正方案：**
```python
def evaluate_predictions_corrected(true_df, pred_df, scaler, feature_start_idx=2):
    # 反标准化到原始尺度
    true_vals_original = scaler.inverse_transform(true_vals_scaled)
    pred_vals_original = scaler.inverse_transform(pred_vals_scaled)
    
    # 在原始尺度上计算评估指标
    mse = mean_squared_error(true_vals_original, pred_vals_original)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(true_vals_original, pred_vals_original)
    r2 = r2_score(true_vals_original, pred_vals_original)
```

## 使用方法

### 1. 环境要求

确保安装以下Python包：
```bash
pip install pandas numpy torch gpytorch scikit-learn
```

### 2. 数据文件要求

确保以下文件在脚本同一目录下：
- `筛选后协变量特征值列表.csv`

### 3. 运行脚本

```bash
python 协变量预测_修正版.py
```

### 4. 输出结果

脚本会生成：
- 控制台输出：每个国家的处理进度和评估指标
- CSV文件：`协变量预测结果_修正版_原始尺度.csv`

## 结果解读

### 评估指标含义

修正后的评估指标具有实际意义：

- **MSE (均方误差)**：在原始数据尺度上的平均平方误差
- **RMSE (均方根误差)**：在原始数据尺度上的均方根误差
- **MAE (平均绝对误差)**：在原始数据尺度上的平均绝对误差
- **R² (决定系数)**：模型解释方差的比例

### 与原始方法的对比

| 方面 | 原始方法 | 修正方法 |
|------|----------|----------|
| 标准化次数 | 2次（重复） | 1次（合理） |
| 评估尺度 | [0,1]标准化尺度 | 原始数据尺度 |
| 结果可解释性 | 低 | 高 |
| 误差真实性 | 失真 | 真实 |

## 数据流程图

```
原始数据
    ↓
异常值处理
    ↓
MinMaxScaler标准化 (保存scaler)
    ↓
训练/测试划分
    ↓
模型训练 (使用标准化数据)
    ↓
模型预测 (输出标准化结果)
    ↓
反标准化 (scaler.inverse_transform)
    ↓
原始尺度评估指标计算
```

## 注意事项

1. **内存管理**：脚本包含GPU内存清理机制，适合大规模数据处理
2. **错误处理**：包含异常处理机制，单个国家处理失败不会影响整体运行
3. **进度显示**：实时显示处理进度和中间结果
4. **结果保存**：自动保存详细结果到CSV文件

## 技术细节

### 关键函数说明

- `load_and_preprocess_data()`: 数据加载和单次标准化
- `process_single_country_corrected()`: 修正版单国家处理
- `evaluate_predictions_corrected()`: 修正版评估函数
- `scalers_dict`: 保存每个国家的标准化器

### 修正验证

脚本运行后会显示：
- 修正前后的问题对比
- 数据流验证信息
- 改进效果说明

## 预期改进效果

1. **准确性提升**：消除重复标准化导致的数据失真
2. **可解释性增强**：评估指标反映真实的预测误差
3. **实用性提高**：结果可直接用于业务决策
4. **科学性保证**：符合时间序列预测的标准做法

## 故障排除

### 常见问题

1. **CUDA内存不足**：脚本会自动清理GPU内存
2. **数据文件缺失**：检查CSV文件是否在正确路径
3. **包依赖问题**：确保安装所有必需的Python包

### 性能优化

- 使用GPU加速（如果可用）
- 自动内存清理
- 批量处理优化

## 联系信息

如有问题或建议，请参考代码注释或联系开发者。
