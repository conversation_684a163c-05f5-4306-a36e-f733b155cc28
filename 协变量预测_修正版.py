"""
协变量预测修正版脚本
修正问题：
1. 消除重复标准化问题
2. 添加预测结果反标准化
3. 修正评估指标计算（在原始尺度上计算）

作者：修正版本
日期：2025-01-11
"""

import pandas as pd
import numpy as np
import torch
import gpytorch
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import MinMaxScaler
import gc
import warnings
warnings.filterwarnings('ignore')

# 检查设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

def load_and_preprocess_data():
    """
    加载和预处理数据
    修正：只进行一次标准化，并保存scaler用于后续反标准化
    """
    print("正在加载数据...")
    filled_df = pd.read_csv("筛选后协变量特征值列表.csv", index_col=[0])
    print(f"数据形状: {filled_df.shape}")
    
    # 按location分组
    grouped = filled_df.groupby('location')
    result_dict = {location: group for location, group in grouped}
    
    # 修正：只进行一次标准化，并保存每个国家的scaler
    print("正在进行数据标准化（仅一次）...")
    result_dict_processed = {}
    scalers_dict = {}  # 保存每个国家的scaler用于反标准化
    
    for country, df in result_dict.items():
        # 提取特征列（从第2列开始，排除location和year）
        features = df.iloc[:, 2:].copy()
        
        # 异常值处理（保持原有逻辑）
        outlier_threshold = 10
        for col in features.columns:
            Q1 = features[col].quantile(0.25)
            Q3 = features[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - outlier_threshold * IQR
            upper_bound = Q3 + outlier_threshold * IQR
            
            # 限制异常值到边界
            outliers = (features[col] < lower_bound) | (features[col] > upper_bound)
            if outliers.sum() > 0:
                features.loc[features[col] < lower_bound, col] = lower_bound
                features.loc[features[col] > upper_bound, col] = upper_bound
        
        # 标准化到 [0, 1]（只进行一次）
        scaler = MinMaxScaler()
        scaled_features = scaler.fit_transform(features)
        
        # 保存scaler用于后续反标准化
        scalers_dict[country] = scaler
        
        # 创建标准化后的DataFrame
        scaled_df = pd.DataFrame(scaled_features, columns=features.columns)
        scaled_df.insert(0, 'location', df['location'].values)
        scaled_df.insert(1, 'year', df['year'].values)
        result_dict_processed[country] = scaled_df
    
    print(f"数据预处理完成，共处理 {len(result_dict_processed)} 个国家")
    return result_dict_processed, scalers_dict

def split_train_test_years(df, test_size=-5):
    """划分训练和测试年份"""
    years = df['year'].sort_values().unique()
    if abs(test_size) >= len(years):
        raise ValueError("Test size must be smaller than the total number of years")
    test_years = years[test_size:].tolist()
    train_years = years[:test_size].tolist()
    return train_years, test_years

def get_train_test_data(df, train_years, test_years):
    """根据年份划分训练和测试数据集"""
    train_df = df[df['year'].isin(train_years)]
    test_df = df[df['year'].isin(test_years)]
    return train_df, test_df

def create_gp_model(X_train, y_train, device):
    """创建并返回高斯过程模型和似然函数"""
    class MultitaskGPModel(gpytorch.models.ExactGP):
        def __init__(self, train_x, train_y, likelihood):
            super(MultitaskGPModel, self).__init__(train_x, train_y, likelihood)
            self.mean_module = gpytorch.means.MultitaskMean(
                gpytorch.means.ConstantMean(), num_tasks=y_train.shape[1]
            )
            self.covar_module = gpytorch.kernels.MultitaskKernel(
                gpytorch.kernels.RBFKernel(), num_tasks=y_train.shape[1], rank=1
            )

        def forward(self, x):
            mean_x = self.mean_module(x)
            covar_x = self.covar_module(x)
            return gpytorch.distributions.MultitaskMultivariateNormal(mean_x, covar_x)

    likelihood = gpytorch.likelihoods.MultitaskGaussianLikelihood(num_tasks=y_train.shape[1]).to(device)
    model = MultitaskGPModel(X_train, y_train, likelihood).to(device)
    return model, likelihood

def train_model(model, likelihood, X_train, y_train, training_iter=50):
    """训练高斯过程模型"""
    model.train()
    likelihood.train()
    
    optimizer = torch.optim.Adam([
        {'params': model.parameters()},
    ], lr=0.1)
    
    mll = gpytorch.mlls.ExactMarginalLogLikelihood(likelihood, model)
    
    for i in range(training_iter):
        optimizer.zero_grad()
        output = model(X_train)
        loss = -mll(output, y_train)
        loss.backward()
        optimizer.step()
        
        if (i + 1) % 10 == 0:
            print(f'  训练迭代 {i + 1}/{training_iter} - Loss: {loss.item():.3f}')
    
    return model, likelihood

def predict_values(model, likelihood, train_length, pred_steps, device):
    """进行预测"""
    model.eval()
    likelihood.eval()
    
    predictions = []
    with torch.no_grad(), gpytorch.settings.fast_pred_var():
        for step in range(pred_steps):
            next_time_step = torch.tensor([[train_length + step]], dtype=torch.float32).to(device)
            pred = likelihood(model(next_time_step)).mean.cpu().numpy()
            predictions.append(pred[0])
    
    return np.array(predictions)

def cleanup_memory():
    """清理内存"""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

def build_prediction_df(predictions, location, test_years, feature_cols):
    """构建预测结果DataFrame"""
    pred_df = pd.DataFrame(predictions, columns=feature_cols)
    pred_df.insert(0, 'location', location)
    pred_df.insert(1, 'year', test_years)
    return pred_df

def evaluate_predictions_corrected(true_df, pred_df, scaler, feature_start_idx=2):
    """
    修正版评估函数：在原始数据尺度上计算评估指标
    
    关键修正：
    1. 使用scaler对预测结果和真实值进行反标准化
    2. 在原始数据尺度上计算MSE、RMSE、MAE、R²
    3. 返回有实际意义的评估指标
    """
    true_df = true_df.set_index('year')
    pred_df = pred_df.set_index('year')
    common_years = true_df.index.intersection(pred_df.index)
    feature_cols = true_df.columns[feature_start_idx:]

    mse_list, rmse_list, mae_list, r2_list = [], [], [], []
    
    for year in common_years:
        # 获取标准化后的真实值和预测值
        true_vals_scaled = true_df.loc[year, feature_cols].values.reshape(1, -1)
        pred_vals_scaled = pred_df.loc[year, feature_cols].values.reshape(1, -1)
        
        # 关键修正：反标准化到原始数据尺度
        true_vals_original = scaler.inverse_transform(true_vals_scaled).flatten()
        pred_vals_original = scaler.inverse_transform(pred_vals_scaled).flatten()
        
        # 在原始尺度上计算评估指标
        mse = mean_squared_error(true_vals_original, pred_vals_original)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(true_vals_original, pred_vals_original)
        r2 = r2_score(true_vals_original, pred_vals_original)
        
        mse_list.append(mse)
        rmse_list.append(rmse)
        mae_list.append(mae)
        r2_list.append(r2)
    
    return [mse_list, rmse_list, mae_list, r2_list]

def process_single_country_corrected(df, scaler, test_size, device):
    """
    修正版单国家处理函数
    
    关键修正：
    1. 移除重复标准化步骤
    2. 直接使用已标准化的数据进行训练
    3. 使用传入的scaler进行反标准化评估
    """
    country = df['location'].iloc[0]
    print(f"正在处理国家: {country}")
    
    # 划分年份和数据
    train_years, test_years = split_train_test_years(df, test_size)
    train_df, test_df = get_train_test_data(df, train_years, test_years)

    # 修正：直接使用已标准化的数据，不再进行第二次标准化
    features_for_training = train_df.iloc[:, 2:]  # 直接使用已标准化的特征
    
    # 准备训练输入
    X_train = torch.tensor(np.arange(len(features_for_training)).reshape(-1, 1), dtype=torch.float32).to(device)
    y_train = torch.tensor(features_for_training.values, dtype=torch.float32).to(device)

    # 创建和训练模型
    model, likelihood = create_gp_model(X_train, y_train, device)
    model, likelihood = train_model(model, likelihood, X_train, y_train)

    # 预测
    pred_steps = len(test_years)
    predictions = predict_values(model, likelihood, len(train_df), pred_steps, device)

    # 清理内存
    cleanup_memory()

    # 构建预测结果
    pred_df = build_prediction_df(predictions, country, test_years, features_for_training.columns)
    
    # 修正：使用原始scaler进行反标准化评估
    metrics = evaluate_predictions_corrected(test_df, pred_df, scaler)
    
    return metrics

def main():
    """主函数"""
    print("=== 协变量预测修正版脚本 ===")
    print("修正内容：")
    print("1. 消除重复标准化问题")
    print("2. 添加预测结果反标准化")
    print("3. 在原始尺度上计算评估指标")
    print("=" * 50)
    
    # 加载和预处理数据（只进行一次标准化）
    result_dict_processed, scalers_dict = load_and_preprocess_data()
    
    # 设置参数
    test_size = -5
    metric_dict = {}
    
    print(f"\n开始处理 {len(result_dict_processed)} 个国家...")
    
    # 处理每个国家
    for j, country in enumerate(result_dict_processed.keys(), 1):
        df_target = result_dict_processed[country]
        country_scaler = scalers_dict[country]  # 获取该国家的scaler
        
        try:
            # 使用修正版处理函数
            metrics = process_single_country_corrected(df_target, country_scaler, test_size, device)
            metric_dict[country] = metrics

            # 计算平均指标（在原始尺度上）
            avg_mse = np.mean(metrics[0][:5]) if len(metrics[0]) >= 5 else np.mean(metrics[0])
            avg_rmse = np.mean(metrics[1][:5]) if len(metrics[1]) >= 5 else np.mean(metrics[1])
            avg_mae = np.mean(metrics[2][:5]) if len(metrics[2]) >= 5 else np.mean(metrics[2])
            avg_r2 = np.mean(metrics[3][:5]) if len(metrics[3]) >= 5 else np.mean(metrics[3])
            
            print(f"国家: {country}")
            print(f"  原始尺度评估指标 - MSE: {avg_mse:.3f}, RMSE: {avg_rmse:.3f}, MAE: {avg_mae:.3f}, R²: {avg_r2:.3f}")
            print(f"  进度: {j}/{len(result_dict_processed)}")
            print("-" * 50)
            
        except Exception as e:
            print(f"处理国家 {country} 时出错: {str(e)}")
            continue
    
    print("\n=== 处理完成 ===")
    print(f"成功处理了 {len(metric_dict)} 个国家")
    
    # 保存结果
    results_summary = []
    for country, metrics in metric_dict.items():
        avg_mse = np.mean(metrics[0][:5]) if len(metrics[0]) >= 5 else np.mean(metrics[0])
        avg_rmse = np.mean(metrics[1][:5]) if len(metrics[1]) >= 5 else np.mean(metrics[1])
        avg_mae = np.mean(metrics[2][:5]) if len(metrics[2]) >= 5 else np.mean(metrics[2])
        avg_r2 = np.mean(metrics[3][:5]) if len(metrics[3]) >= 5 else np.mean(metrics[3])
        
        results_summary.append({
            'Country': country,
            'MSE_Original_Scale': avg_mse,
            'RMSE_Original_Scale': avg_rmse,
            'MAE_Original_Scale': avg_mae,
            'R2_Original_Scale': avg_r2
        })
    
    # 保存到CSV文件
    results_df = pd.DataFrame(results_summary)
    results_df.to_csv('协变量预测结果_修正版_原始尺度.csv', index=False, encoding='utf-8-sig')
    print(f"结果已保存到: 协变量预测结果_修正版_原始尺度.csv")
    
    return metric_dict

def compare_with_original_method():
    """
    可选：与原始方法进行对比分析
    展示修正前后评估指标的差异
    """
    print("\n=== 修正效果说明 ===")
    print("原始方法问题：")
    print("1. 重复标准化：数据被标准化两次，导致分布失真")
    print("2. 评估尺度错误：在[0,1]标准化尺度上计算评估指标")
    print("3. 结果不可解释：无法反映真实的预测误差大小")
    print()
    print("修正后改进：")
    print("1. 单次标准化：只在预处理阶段进行一次标准化")
    print("2. 反标准化评估：预测结果反标准化后在原始尺度上评估")
    print("3. 真实误差：评估指标反映实际的预测误差大小")
    print("4. 可解释性：结果具有实际业务意义")

def validate_data_flow():
    """
    验证数据流的正确性
    """
    print("\n=== 数据流验证 ===")
    print("修正后的数据流：")
    print("1. 原始数据 → 异常值处理 → MinMaxScaler标准化 → 保存scaler")
    print("2. 标准化数据 → 训练/测试划分 → 直接用于模型训练")
    print("3. 模型预测 → 预测结果 → scaler.inverse_transform → 原始尺度")
    print("4. 原始尺度真实值 vs 原始尺度预测值 → 计算评估指标")

if __name__ == "__main__":
    # 运行主程序
    metric_dict = main()

    # 显示修正效果说明
    compare_with_original_method()

    # 验证数据流
    validate_data_flow()

    print("\n=== 脚本执行完成 ===")
    print("请查看生成的CSV文件获取详细结果")
